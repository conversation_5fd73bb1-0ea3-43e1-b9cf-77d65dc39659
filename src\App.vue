<template>
  <div id="app-32-map" class="is-full"></div>
</template>

<script>
import Map3d from "@/utils/Map3d.js"
import TWEEN from "@tweenjs/tween.js"
import * as THREE from "three"
import { onBeforeUnmount, onMounted } from "vue"
import { random } from "@/utils"
import useFileLoader from "@/hooks/useFileLoader.js"
import useCountry from "@/hooks/useCountry.js"
import useCoord from "@/hooks/useCoord.js"
import useConversionStandardData from "@/hooks/useConversionStandardData.js"
import useMapMarkedLightPillar from "@/hooks/map/useMapMarkedLightPillar"
import useSequenceFrameAnimate from "@/hooks/useSequenceFrameAnimate"
import useCSS2DRender from "@/hooks/useCSS2DRender"

let centerXY = [111.75848936398664, 39.25361092367786]

export default {
  name: "3dMap30",
  setup () {
    let baseEarth = null

    // 重置
    const resize = () => {
      baseEarth.resize()
    }

    const { requestData } = useFileLoader()
    const { transfromGeoJSON } = useConversionStandardData()
    const { getBoundingBox } = useCoord()
    const { createCountryFlatLine } = useCountry()
    const { initCSS2DRender, create2DTag } = useCSS2DRender()
    const { createLightPillar } = useMapMarkedLightPillar({
      scaleFactor: 0.22,
    })
    // 序列帧
    const { createSequenceFrame } = useSequenceFrameAnimate()

    const texture = new THREE.TextureLoader()
    const textureMap = texture.load("/data/map/gz-map.jpg")
    const texturefxMap = texture.load("/data/map/gz-map-fx.jpg")
    const rotatingApertureTexture = texture.load("/data/map/rotatingAperture.png")
    const rotatingPointTexture = texture.load("/data/map/rotating-point2.png")
    const circlePoint = texture.load("/data/map/circle-point.png")
    const sceneBg = texture.load("/data/map/scene-bg2.png")
    textureMap.wrapS = texturefxMap.wrapS = THREE.RepeatWrapping
    textureMap.wrapT = texturefxMap.wrapT = THREE.RepeatWrapping
    textureMap.flipY = texturefxMap.flipY = false
    textureMap.rotation = texturefxMap.rotation = THREE.MathUtils.degToRad(45)
    const scale = 0.6
    textureMap.repeat.set(scale, scale)
    texturefxMap.repeat.set(scale, scale)
    const topFaceMaterial = new THREE.MeshPhongMaterial({
      map: textureMap,
      color: 0xb4eeea,
      combine: THREE.MultiplyOperation,
      transparent: true,
      opacity: 1,
    })
    const createGradientMaterial = () => {
      const uniforms = {
        topColor: { value: new THREE.Color(0x0c3c2e) }, // 顶部颜色
        bottomColor: { value: new THREE.Color(0x000000) }, // 底部颜色，黑色
        opacity: { value: 1.0 }
      };

      const vertexShader = `
        varying vec3 vWorldPosition;
        void main() {
        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vWorldPosition = worldPosition.xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `;

      const fragmentShader = `
        uniform vec3 topColor;
        uniform vec3 bottomColor;
        uniform float opacity;
        varying vec3 vWorldPosition;

        void main() {
        // 计算垂直方向渐变因子（0=底部, 1=顶部）
        // 基于拉伸深度0.07来计算渐变
        float factor = clamp((vWorldPosition.z + 0.035) / 0.07, 0.0, 1.0);
        // 混合底部和顶部颜色，factor=0时为底部颜色，factor=1时为顶部颜色
        vec3 color = mix(bottomColor, topColor, factor);
        gl_FragColor = vec4(color, opacity);
        }
      `;

      return new THREE.ShaderMaterial({
        uniforms,
        vertexShader,
        fragmentShader,
        transparent: true,
        depthTest: true,
        side: THREE.FrontSide
      });
    };

    const sideMaterial = createGradientMaterial();
    const bottomZ = -0.1

    // 创建线条发光阴影效果
    const createLineShadowEffect = (originalLine, shadowOptions = {}) => {
      const defaultOptions = {
        shadowColor: 0x000000,
        shadowOpacity: 0.4,
        shadowBlur: 3, // 模糊层数
        shadowOffset: { x: 0.003, y: -0.003, z: 0.7 }
      };
      const options = { ...defaultOptions, ...shadowOptions };

      const shadowGroup = new THREE.Group();

      // 创建多层阴影实现模糊效果
      for (let i = 0; i < options.shadowBlur; i++) {
        const shadowLayer = originalLine.clone();
        const layerOpacity = options.shadowOpacity * (1 - i / options.shadowBlur);
        const layerOffset = i * 0.001;

        // 遍历阴影层的所有材质
        shadowLayer.traverse((child) => {
          if (child.material) {
            if (child.material.clone) {
              child.material = child.material.clone();
              child.material.color.setHex(options.shadowColor);
              child.material.opacity = layerOpacity;
              child.material.transparent = true;
            }
          }
        });

        shadowLayer.position.set(
          options.shadowOffset.x + layerOffset,
          options.shadowOffset.y - layerOffset,
          options.shadowOffset.z - layerOffset
        );

        shadowGroup.add(shadowLayer);
      }

      return shadowGroup;
    };
    // 初始化旋转光圈
    const initRotatingAperture = (scene, width) => {
      let plane = new THREE.PlaneBufferGeometry(width, width)
      let material = new THREE.MeshBasicMaterial({
        map: rotatingApertureTexture,
        transparent: true,
        opacity: 1,
        depthTest: true,
      })
      let mesh = new THREE.Mesh(plane, material)
      mesh.position.set(...centerXY, 0)
      mesh.scale.set(0.57, 0.57, 0.57)
      scene.add(mesh)
      return mesh
    }
    // 初始化旋转点
    const initRotatingPoint = (scene, width) => {
      let plane = new THREE.PlaneBufferGeometry(width, width)
      let material = new THREE.MeshBasicMaterial({
        map: rotatingPointTexture,
        transparent: true,
        opacity: 1,
        depthTest: true,
      })
      let mesh = new THREE.Mesh(plane, material)
      mesh.position.set(...centerXY, bottomZ - 0.02)
      mesh.scale.set(1.1, 1.1, 1.1)
      scene.add(mesh)
      return mesh
    }
    // 初始化背景
    const initSceneBg = (scene, width) => {
      let plane = new THREE.PlaneBufferGeometry(width * 4, width * 4)
      let material = new THREE.MeshPhongMaterial({
        // color: 0x061920,
        color: 0xffffff,
        map: sceneBg,
        transparent: true,
        opacity: 1,
        depthTest: true,
      })

      let mesh = new THREE.Mesh(plane, material)
      mesh.position.set(...centerXY, bottomZ - 0.2)
      scene.add(mesh)
    }
    // 初始化原点
    const initCirclePoint = (scene, width) => {
      let plane = new THREE.PlaneBufferGeometry(width, width)
      let material = new THREE.MeshPhongMaterial({
        color: 0x00ffff,
        map: circlePoint,
        transparent: true,
        opacity: 1,
        // depthTest: false,
      })
      let mesh = new THREE.Mesh(plane, material)
      mesh.position.set(...centerXY, bottomZ - 0.1)
      // let mesh2 = mesh.clone()
      // mesh2.position.set(...centerXY, bottomZ - 0.001)
      scene.add(mesh)
    }
    // 初始化粒子
    const initParticle = (scene, bound) => {
      // 获取中心点和中间地图大小
      let { center, size } = bound
      // 构建范围，中间地图的2倍
      let minX = center.x - size.x
      let maxX = center.x + size.x
      let minY = center.y - size.y
      let maxY = center.y + size.y
      let minZ = -6
      let maxZ = 6

      let particleArr = []
      for (let i = 0; i < 16; i++) {
        const particle = createSequenceFrame({
          image: "./data/map/上升粒子1.png",
          width: 60,
          height: 89,
          frame: 9,
          column: 9,
          row: 1,
          speed: 0.5,
        })
        let particleScale = random(5, 10) / 1000
        particle.scale.set(particleScale, particleScale, particleScale)
        particle.rotation.x = Math.PI / 2
        let x = random(minX, maxX)
        let y = random(minY, maxY)
        let z = random(minZ, maxZ)
        particle.position.set(x, y, z)
        particleArr.push(particle)
      }
      scene.add(...particleArr)
      return particleArr
    }
    // 创建移动光带材质
    const createMovingLightMaterial = () => {
      const vertexShader = `
        uniform float uTime;
        uniform float uSpeed;
        uniform float uProgress;
        uniform float uLength;
        varying vec2 vUv;
        varying float vAlpha;

        void main() {
          vUv = uv;

          // 计算当前片段在整个路径中的位置
          float segmentProgress = uv.x;

          // 计算光带的可见性
          float lightStart = uProgress - uLength;
          float lightEnd = uProgress;

          // 创建渐变消失效果
          float alpha = 0.0;
          if (segmentProgress >= lightStart && segmentProgress <= lightEnd) {
            float localProgress = (segmentProgress - lightStart) / uLength;
            // 前端亮，后端逐渐消失
            alpha = smoothstep(0.0, 0.3, localProgress) * smoothstep(1.0, 0.7, localProgress);
          }

          vAlpha = alpha;

          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `;

      const fragmentShader = `
        uniform vec3 uColor;
        uniform vec3 uGlowColor;
        uniform float uIntensity;
        varying vec2 vUv;
        varying float vAlpha;

        void main() {
          if (vAlpha <= 0.01) {
            discard; // 完全透明的部分不渲染
          }

          // 径向渐变效果 - 中心亮，边缘暗
          float radialGradient = 1.0 - abs(vUv.y - 0.5) * 2.0;
          radialGradient = pow(radialGradient, 2.0);

          // 使用单一颜色，通过亮度变化创建效果
          vec3 finalColor = uColor * (0.3 + vAlpha * radialGradient * uIntensity);

          // 不透明渲染
          gl_FragColor = vec4(finalColor, 1.0);
        }
      `;

      return new THREE.ShaderMaterial({
        uniforms: {
          uTime: { value: 0.0 },
          uSpeed: { value: 0.1 }, // 移动速度
          uProgress: { value: 0.0 }, // 当前进度 (0-1)
          uLength: { value: 0.3 }, // 光带长度 (占总路径的比例)
          uColor: { value: new THREE.Color(0x0dcd74) }, // 主颜色 #50f9ff
          uGlowColor: { value: new THREE.Color(0xffffff) }, // 发光颜色也设为同色
          uIntensity: { value: 4 } // 整体强度
        },
        vertexShader,
        fragmentShader,
        transparent: false, // 不透明
        side: THREE.DoubleSide,
        blending: THREE.NormalBlending, // 正常混合模式
        depthWrite: true
      });
    };

    // 创建移动光带
    const createMovingLightTube = (points, radius = 0.01) => {
      // 创建曲线
      const curve = new THREE.CatmullRomCurve3(points, false);

      // 创建管道几何体
      const geometry = new THREE.TubeGeometry(curve, 200, radius, 8, false);

      // 创建材质
      const material = createMovingLightMaterial();

      // 创建网格
      const mesh = new THREE.Mesh(geometry, material);

      return { mesh, material, curve };
    };

    // 创建顶部底部边线和流光管道
    const initBorderLine = (data, mapGroup) => {
      // 创建顶部边线
      let lineTop = createCountryFlatLine(
        data,
        {
          color: 0xffffff,
          linewidth: 0.002,
          transparent: true,
          depthTest: false,
        },
        "Line2"
      )
      lineTop.position.z += 0.07

      // 创建高级阴影效果
      const shadowEffect = createLineShadowEffect(lineTop, {
        shadowColor: 0x000000,
        shadowOpacity: 0.5,
        shadowBlur: 4,
        shadowOffset: { x: 0.004, y: -0.004, z: 0.07 }
      });

      mapGroup.add(shadowEffect) // 先添加阴影
      mapGroup.add(lineTop) // 再添加主线条

      // 创建单个移动光带，只使用第一个feature的geometry
      let movingLightTube = null;

      if (data.features && data.features.length > 0) {
        const firstFeature = data.features[0]; // 只取下标0的feature
        const coordinates = firstFeature.geometry.coordinates;

        if (coordinates && coordinates.length > 0) {
          // 取第一个坐标组的第一个多边形
          const polygon = coordinates[0][0];

          // 将多边形坐标转换为Three.js Vector3点
          const points = polygon.map(coord =>
            new THREE.Vector3(coord[0], coord[1], 0)
          );

          // 确保路径闭合
          if (points.length > 0) {
            points.push(points[0].clone());
          }

          // 创建单个移动光带
          const { mesh, material, curve } = createMovingLightTube(points, 0.004);
          movingLightTube = {
            mesh,
            material,
            curve,
            startTime: 0 // 单管道不需要随机时间
          };
          mapGroup.add(mesh);
        }
      }

      // 返回单个移动光带对象
      return movingLightTube;
    }
    // 创建光柱
    const initLightPoint = (properties, mapGroup) => {
      if (!properties.centroid && !properties.center) {
        return false
      }
      // 创建光柱
      let heightScaleFactor = 0.08 + random(1, 4) / 6
      let lightCenter = properties.centroid || properties.center
      let light = createLightPillar(...lightCenter, heightScaleFactor)
      light.position.z = 0.08
      mapGroup.add(light)
    }
    // 创建标签
    const initLabel = (properties, scene) => {
      if (!properties.centroid && !properties.center) {
        return false
      }
      // 设置标签的显示内容和位置
      var label = create2DTag("标签", "map-32-label")
      scene.add(label)
      let labelCenter = properties.center  //centroid || properties.center
      label.show(properties.name, new THREE.Vector3(...labelCenter, 0.12))
    }
    onMounted(async () => {
      // 四川数据
      let provinceData = await requestData("./data/map/和林格尔县.json")
      provinceData = transfromGeoJSON(provinceData)

      class CurrentMap3d extends Map3d {
        constructor(props) {
          super(props)
        }
        initCamera () {
          let { width, height } = this.options
          let rate = width / height
          // 设置45°的透视相机,更符合人眼观察
          this.camera = new THREE.PerspectiveCamera(45, rate, 0.001, 90000000)
          this.camera.up.set(0, 0, 1)
          // 贵州
          // this.camera.position.set(105.96420078859111, 20.405756412693812, 5.27483892390678) //相机在Three.js坐标系中的位置
          // 四川
          this.camera.position.set(111.81503863178989, 39.56307944384293, 1.027904422793993) //相机在Three.js坐标系中的位置
          this.camera.lookAt(...centerXY, 0)

        }
        initModel () {
          try {
            // 创建组
            this.mapGroup = new THREE.Group()
            // 标签 初始化
            this.css2dRender = initCSS2DRender(this.options, this.container)

            provinceData.features.forEach((elem, index) => {
              // 定一个省份对象
              const province = new THREE.Object3D()
              // 坐标
              const coordinates = elem.geometry.coordinates
              // city 属性
              const properties = elem.properties

              // 循环坐标
              coordinates.forEach((multiPolygon) => {
                multiPolygon.forEach((polygon) => {
                  const shape = new THREE.Shape()
                  // 绘制shape
                  for (let i = 0; i < polygon.length; i++) {
                    let [x, y] = polygon[i]
                    if (i === 0) {
                      shape.moveTo(x, y)
                    }
                    shape.lineTo(x, y)
                  }
                  // 拉伸设置
                  const extrudeSettings = {
                    depth: 0.07,
                    bevelEnabled: false,
                    bevelSegments: 2,
                    bevelThickness: 0.1,
                  }
                  const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings)
                  const mesh = new THREE.Mesh(geometry, [topFaceMaterial, sideMaterial])
                  province.add(mesh)
                })
              })
              this.mapGroup.add(province)
              // 创建标点和标签
              initLightPoint(properties, this.mapGroup)
              initLabel(properties, this.scene)
            })
            // 创建上下边框和单个移动光带
            this.movingLightTube = initBorderLine(provinceData, this.mapGroup)

            let earthGroupBound = getBoundingBox(this.mapGroup)
            centerXY = [earthGroupBound.center.x, earthGroupBound.center.y]
            let { size } = earthGroupBound
            let width = size.x < size.y ? size.y + 1 : size.x + 1
            // 添加背景，修饰元素
            this.rotatingApertureMesh = initRotatingAperture(this.scene, width)
            this.rotatingPointMesh = initRotatingPoint(this.scene, width - 2)
            initCirclePoint(this.scene, width)
            initSceneBg(this.scene, width)

            // 将组添加到场景中
            this.scene.add(this.mapGroup)
            this.particleArr = initParticle(this.scene, earthGroupBound)
            // initGui()
          } catch (error) {
            console.log(error)
          }
        }
        getDataRenderMap () { }

        destroy () { }
        initControls () {
          super.initControls()
          this.controls.target = new THREE.Vector3(...centerXY, 0)
          this.controls.minDistance = 1  // 最小缩放距离
          this.controls.maxDistance = 2  // 最大缩放距离
          this.controls.maxPolarAngle = (Math.PI / 2) * 0.8  // 最大旋转角度
          console.log(this.controls);
        }

        initLight () {
          //   平行光1
          let directionalLight1 = new THREE.DirectionalLight(0x7af4ff, 1)
          directionalLight1.position.set(...centerXY, 30)
          //   平行光2
          let directionalLight2 = new THREE.DirectionalLight(0x7af4ff, 1)
          directionalLight2.position.set(...centerXY, 30)
          // 环境光
          let ambientLight = new THREE.AmbientLight(0x7af4ff, 1)
          // 将光源添加到场景中
          this.addObject(directionalLight1)
          this.addObject(directionalLight2)
          this.addObject(ambientLight)
        }
        initRenderer () {
          super.initRenderer()
          // this.renderer.outputEncoding = THREE.sRGBEncoding
        }
        loop () {
          this.animationStop = window.requestAnimationFrame(() => {
            this.loop()
          })
          // 这里是你自己业务上需要的code
          this.renderer.render(this.scene, this.camera)
          // 控制相机旋转缩放的更新
          if (this.options.controls.visibel && this.controls) {
            // this.controls.target.set(...centerXY, 0)
            this.controls.update()
          }
          // 统计更新
          // if (this.options.statsVisibel) this.stats.update()
          if (this.rotatingApertureMesh) {
            this.rotatingApertureMesh.rotation.z += 0.0005
          }
          if (this.rotatingPointMesh) {
            this.rotatingPointMesh.rotation.z -= 0.0005
          }
          // 渲染标签
          if (this.css2dRender) {
            this.css2dRender.render(this.scene, this.camera)
          }
          // 粒子上升
          if (this.particleArr.length) {
            for (let i = 0; i < this.particleArr.length; i++) {
              this.particleArr[i].updateSequenceFrame()
              this.particleArr[i].position.z += 0.01
              if (this.particleArr[i].position.z >= 6) {
                this.particleArr[i].position.z = -6
              }
            }
          }

          // 更新单个移动光带动画
          if (this.movingLightTube && this.movingLightTube.material && this.movingLightTube.material.uniforms) {
            const time = this.clock ? this.clock.elapsedTime : Date.now() * 0.001;
            const adjustedTime = time + this.movingLightTube.startTime;
            this.movingLightTube.material.uniforms.uTime.value = adjustedTime;

            // 计算光带在路径上的进度 (0-1循环)
            const speed = this.movingLightTube.material.uniforms.uSpeed.value;
            const progress = (adjustedTime * speed) % 1.0;
            this.movingLightTube.material.uniforms.uProgress.value = progress;
          }

          TWEEN.update()

          // console.log(this.camera.position)
        }
        resize () {
          super.resize()
          // 这里是你自己业务上需要的code
          this.renderer.render(this.scene, this.camera)
          this.renderer.setPixelRatio(window.devicePixelRatio)

          if (this.css2dRender) {
            this.css2dRender.setSize(this.options.width, this.options.height)
          }
        }
      }
      baseEarth = new CurrentMap3d({
        container: "#app-32-map",
        axesVisibel: false,
        controls: {
          enableDamping: true, // 阻尼
          maxPolarAngle: (Math.PI / 2) * 0.98,
          minDistance: 200
        },
      })


      baseEarth.run()
      window.addEventListener("resize", resize)
    })
    onBeforeUnmount(() => {
      window.removeEventListener("resize", resize)
    })
  },
}
</script>
<style>
html,
body,
#app,
.is-full {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.map-32-label {
  font-size: 15px;
  font-weight: 700;
  color: #fff;
}
</style>
