{"name": "globe-map", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@tweenjs/tween.js": "^18.6.4", "delaunator": "^5.0.0", "geo-point-in-polygon": "^1.0.0", "lodash.max": "^4.0.1", "normalize.css": "^8.0.1", "point-in-polygon": "^1.1.0", "three": "^0.136.0", "vue": "^3.2.47", "vue-router": "^4.2.1"}, "devDependencies": {"@types/three": "^0.161.0", "@vitejs/plugin-vue": "^4.1.0", "sass": "^1.62.1", "vite": "^4.3.2"}}